/**
 * Duplicate Prevention Utilities
 *
 * Utility functions for creating and managing duplicate prevention locks
 * in webhook processors. Provides a clean, reusable API for lock creation
 * that can be used across different webhook processors.
 *
 * **Key Features:**
 * - Simple, user-friendly API for lock creation
 * - Automatic validation and error handling
 * - Consistent logging across all webhook processors
 * - Support for both patient and appointment locks
 * - Integration with WebhookQueueManager
 *
 * @fileoverview Utility functions for duplicate prevention lock management
 * @version 1.0.0
 * @since 2024-08-06
 */

import { logDebug, logWarn } from "@utils/logger";
import type { PlatformSource } from "./types";
import type { WebhookQueueManager } from "./WebhookQueueManager";

/**
 * Options for creating a duplicate prevention lock
 */
export interface CreateLockOptions {
	/** Webhook queue manager instance */
	queueManager: WebhookQueueManager;
	/** ID of the webhook making the API call */
	webhookId: string;
	/** Target platform that will receive the API call */
	targetPlatform: PlatformSource;
	/** Patient ID for patient-related locks */
	patientId?: string;
	/** Appointment ID for appointment-related locks */
	appointmentId?: string;
	/** Source platform entity ID (fallback for new patients) */
	sourceEntityId?: string;
	/** Lock duration in seconds (default: 60) */
	lockDurationSeconds?: number;
}

/**
 * Create a duplicate prevention lock before making cross-platform API calls
 *
 * This utility function provides a clean, user-friendly API for creating
 * duplicate prevention locks in webhook processors. It handles validation,
 * error handling, and logging automatically.
 *
 * **Usage in Webhook Processors:**
 * ```typescript
 * // Before making AP API call from CC webhook
 * await createDuplicatePreventionLock({
 *   queueManager: context.config.queueManager,
 *   webhookId: context.config.webhookId,
 *   targetPlatform: "ap",
 *   patientId: lookupResult.patient?.id,
 * });
 * ```
 *
 * **Features:**
 * - Automatic validation of required parameters
 * - Graceful handling of missing patient/appointment IDs
 * - Consistent logging with appropriate log levels
 * - Error handling that doesn't block webhook processing
 * - Support for both patient and appointment locks
 *
 * @param options - Lock creation options
 * @returns Promise that resolves when lock is created (or skipped)
 */
export async function createDuplicatePreventionLock(
	options: CreateLockOptions,
): Promise<void> {
	const {
		queueManager,
		webhookId,
		targetPlatform,
		patientId,
		appointmentId,
		sourceEntityId,
		lockDurationSeconds = 60,
	} = options;

	// Validate required parameters
	if (!queueManager) {
		logWarn(
			"Cannot create duplicate prevention lock: queueManager not provided",
			{
				webhookId,
				targetPlatform,
			},
		);
		return;
	}

	if (!webhookId) {
		logWarn("Cannot create duplicate prevention lock: webhookId not provided", {
			targetPlatform,
			patientId,
			appointmentId,
		});
		return;
	}

	// Check if we have either patientId or appointmentId
	if (!patientId && !appointmentId) {
		logWarn(
			"Cannot create duplicate prevention lock: no patientId or appointmentId provided",
			{
				webhookId,
				targetPlatform,
			},
		);
		return;
	}

	try {
		// Create the lock using the queue manager
		await queueManager.createDuplicatePreventionLock(
			webhookId,
			targetPlatform,
			patientId,
			appointmentId,
			sourceEntityId,
		);

		// Log successful lock creation
		logDebug(
			`Created duplicate prevention lock for ${targetPlatform.toUpperCase()} API call`,
			{
				webhookId,
				targetPlatform,
				patientId,
				appointmentId,
				lockDurationSeconds,
			},
		);
	} catch (error) {
		// Log error but don't throw - lock creation failures shouldn't block webhook processing
		logWarn("Failed to create duplicate prevention lock", {
			error: error instanceof Error ? error.message : String(error),
			webhookId,
			targetPlatform,
			patientId,
			appointmentId,
		});
	}
}

/**
 * Create a duplicate prevention lock with context validation
 *
 * Convenience function that extracts the necessary parameters from the
 * webhook processing context and creates a duplicate prevention lock.
 * This is the most user-friendly API for webhook processors.
 *
 * **Usage:**
 * ```typescript
 * // In webhook processor
 * await createLockFromContext(context, "ap", lookupResult.patient?.id);
 * ```
 *
 * @param context - Webhook processing context
 * @param targetPlatform - Target platform for the API call
 * @param patientId - Patient ID for patient-related locks
 * @param appointmentId - Appointment ID for appointment-related locks
 * @returns Promise that resolves when lock is created (or skipped)
 */
export async function createLockFromContext(
	context: {
		config?: {
			webhookId?: string;
			queueManager?:
				| WebhookQueueManager
				| {
						createDuplicatePreventionLock: (options: {
							webhookId: string;
							targetPlatform: PlatformSource;
							patientId?: string;
							appointmentId?: string;
							sourceEntityId?: string;
							lockDurationSeconds?: number;
						}) => Promise<void>;
				  };
		};
		payload?: {
			id?: number;
			contact_id?: string;
		};
	},
	targetPlatform: PlatformSource,
	patientId?: string,
	appointmentId?: string,
): Promise<void> {
	// Validate context
	if (!context.config?.webhookId || !context.config?.queueManager) {
		logWarn(
			"Cannot create duplicate prevention lock: missing context configuration",
			{
				hasWebhookId: !!context.config?.webhookId,
				hasQueueManager: !!context.config?.queueManager,
				targetPlatform,
				patientId,
				appointmentId,
			},
		);
		return;
	}

	// Extract source entity ID from context payload
	const sourceEntityId =
		context.payload?.id?.toString() || context.payload?.contact_id;

	// Handle both full WebhookQueueManager and simplified interface
	const queueManager = context.config.queueManager;

	// Check if it's the full WebhookQueueManager (has more methods)
	if ("addWebhook" in queueManager) {
		// Use the main utility function for full WebhookQueueManager
		await createDuplicatePreventionLock({
			queueManager,
			webhookId: context.config.webhookId,
			targetPlatform,
			patientId,
			appointmentId,
			sourceEntityId,
		});
	} else {
		// Use the simplified interface directly
		await queueManager.createDuplicatePreventionLock({
			webhookId: context.config.webhookId,
			targetPlatform,
			patientId,
			appointmentId,
			sourceEntityId,
		});
	}
}
