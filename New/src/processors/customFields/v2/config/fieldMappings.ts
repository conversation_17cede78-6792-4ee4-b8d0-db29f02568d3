/**
 * Field Type Mapping Configuration v2
 *
 * Centralized configuration for field type mappings between AutoPatient (AP)
 * and CliniCore (CC) platforms with comprehensive type compatibility rules.
 *
 * @fileoverview v2 Field type mapping configuration
 * @version 2.0.0
 * @since 2024-08-07
 */

import type {
	APFieldDataType,
	CCFieldType,
	MultiValueConfig,
	TextboxListConfig,
	TypeCompatibilityResult,
} from "../types/index.js";

/**
 * Field type mapping rule
 */
export interface FieldTypeMapping {
	sourceType: APFieldDataType | CCFieldType;
	targetType: CCFieldType | APFieldDataType;
	allowMultipleValues?: boolean;
	skip?: boolean;
	requiresSpecialHandling?: boolean;
	notes?: string;
}

/**
 * AutoPatient to CliniCore field type mappings
 */
export const AP_TO_CC_MAPPINGS: FieldTypeMapping[] = [
	{
		sourceType: "TEXT",
		targetType: "text",
		notes: "Basic text field mapping",
	},
	{
		sourceType: "LARGE_TEXT",
		targetType: "textarea",
		notes: "Large text to textarea mapping",
	},
	{
		sourceType: "NUMERICAL",
		targetType: "number",
		notes: "Numerical to number mapping",
	},
	{
		sourceType: "PHONE",
		targetType: "telephone",
		notes: "Phone to telephone mapping",
	},
	{
		sourceType: "MONETORY",
		targetType: "text",
		notes: "Monetary values stored as text in CC",
	},
	{
		sourceType: "CHECKBOX",
		targetType: "select",
		allowMultipleValues: true,
		notes: "Checkbox to multi-select mapping",
	},
	{
		sourceType: "SINGLE_OPTIONS",
		targetType: "select",
		allowMultipleValues: false,
		notes: "Single options to single-select mapping",
	},
	{
		sourceType: "MULTIPLE_OPTIONS",
		targetType: "select",
		allowMultipleValues: true,
		notes: "Multiple options to multi-select mapping",
	},
	{
		sourceType: "DATE",
		targetType: "date",
		notes: "Date field mapping",
	},
	{
		sourceType: "RADIO",
		targetType: "select",
		allowMultipleValues: false,
		notes: "Radio to single-select mapping (or boolean for Yes/No)",
	},
	{
		sourceType: "EMAIL",
		targetType: "email",
		notes: "Email field mapping",
	},
	{
		sourceType: "TEXTBOX_LIST",
		targetType: "text",
		allowMultipleValues: true,
		requiresSpecialHandling: true,
		notes: "Textbox list to multi-value text mapping",
	},
	{
		sourceType: "FILE_UPLOAD",
		targetType: "text",
		skip: true,
		notes: "File upload fields are skipped entirely",
	},
];

/**
 * CliniCore to AutoPatient field type mappings
 */
export const CC_TO_AP_MAPPINGS: FieldTypeMapping[] = [
	{
		sourceType: "text",
		targetType: "TEXT",
		notes: "Basic text field mapping",
	},
	{
		sourceType: "text",
		targetType: "TEXTBOX_LIST",
		allowMultipleValues: true,
		requiresSpecialHandling: true,
		notes: "Multi-value text to TEXTBOX_LIST mapping",
	},
	{
		sourceType: "textarea",
		targetType: "LARGE_TEXT",
		notes: "Textarea to large text mapping",
	},
	{
		sourceType: "textarea",
		targetType: "TEXTBOX_LIST",
		allowMultipleValues: true,
		requiresSpecialHandling: true,
		notes: "Multi-value textarea to TEXTBOX_LIST mapping",
	},
	{
		sourceType: "select",
		targetType: "MULTIPLE_OPTIONS",
		allowMultipleValues: true,
		notes: "Multi-select to multiple options mapping",
	},
	{
		sourceType: "select",
		targetType: "SINGLE_OPTIONS",
		allowMultipleValues: false,
		notes: "Single-select to single options mapping",
	},
	{
		sourceType: "boolean",
		targetType: "RADIO",
		notes: "Boolean to radio with Yes/No options",
	},
	{
		sourceType: "select-or-custom",
		targetType: "SINGLE_OPTIONS",
		notes: "Select-or-custom to single options mapping",
	},
	{
		sourceType: "number",
		targetType: "NUMERICAL",
		notes: "Number to numerical mapping",
	},
	{
		sourceType: "number",
		targetType: "TEXTBOX_LIST",
		allowMultipleValues: true,
		requiresSpecialHandling: true,
		notes: "Multi-value number to TEXTBOX_LIST mapping",
	},
	{
		sourceType: "telephone",
		targetType: "PHONE",
		notes: "Telephone to phone mapping",
	},
	{
		sourceType: "telephone",
		targetType: "TEXTBOX_LIST",
		allowMultipleValues: true,
		requiresSpecialHandling: true,
		notes: "Multi-value telephone to TEXTBOX_LIST mapping",
	},
	{
		sourceType: "email",
		targetType: "EMAIL",
		notes: "Email field mapping",
	},
	{
		sourceType: "email",
		targetType: "TEXTBOX_LIST",
		allowMultipleValues: true,
		requiresSpecialHandling: true,
		notes: "Multi-value email to TEXTBOX_LIST mapping",
	},
	{
		sourceType: "date",
		targetType: "DATE",
		notes: "Date field mapping",
	},
	{
		sourceType: "medication",
		targetType: "TEXT",
		notes: "Medication field mapped to TEXT",
	},
	{
		sourceType: "permanent-diagnoses",
		targetType: "TEXT",
		notes: "Permanent diagnoses field mapped to TEXT",
	},
	{
		sourceType: "patient-has-recommended",
		targetType: "TEXT",
		notes: "Patient-has-recommended field mapped to TEXT",
	},
];

/**
 * Multi-value conversion configuration
 */
export const MULTI_VALUE_CONFIG: MultiValueConfig = {
	textSeparator: " | ", // For multi-value → TEXT conversion
	listSeparator: ",", // For TEXTBOX_LIST conversion
	preserveEmptyValues: false,
	normalizeValues: true,
};

/**
 * TEXTBOX_LIST specific configuration
 */
export const TEXTBOX_LIST_CONFIG: TextboxListConfig = {
	separator: ",",
	preserveOrder: true,
	allowEmpty: false,
	trimValues: true,
};

/**
 * CC field types compatible with TEXTBOX_LIST
 */
export const TEXTBOX_LIST_COMPATIBLE_CC_TYPES: CCFieldType[] = [
	"text",
	"number",
	"textarea",
	"telephone",
	"email",
];

/**
 * Boolean conversion options for RADIO fields
 */
export const BOOLEAN_RADIO_OPTIONS = {
	ENGLISH: ["Yes", "No"],
	GERMAN: ["Ja", "Nein"],
	MIXED: ["Yes", "Ja", "No", "Nein"],
} as const;

/**
 * Get AP to CC field type mapping
 */
export function getApToCcMapping(
	apType: APFieldDataType,
	allowMultipleValues?: boolean,
): FieldTypeMapping | null {
	return (
		AP_TO_CC_MAPPINGS.find(
			(mapping) =>
				mapping.sourceType === apType &&
				(allowMultipleValues === undefined ||
					mapping.allowMultipleValues === allowMultipleValues),
		) || null
	);
}

/**
 * Get CC to AP field type mapping
 */
export function getCcToApMapping(
	ccType: CCFieldType,
	allowMultipleValues?: boolean,
): FieldTypeMapping | null {
	// For multi-value fields, prioritize TEXTBOX_LIST mappings
	if (
		allowMultipleValues &&
		TEXTBOX_LIST_COMPATIBLE_CC_TYPES.includes(ccType)
	) {
		const textboxMapping = CC_TO_AP_MAPPINGS.find(
			(mapping) =>
				mapping.sourceType === ccType &&
				mapping.targetType === "TEXTBOX_LIST" &&
				mapping.allowMultipleValues === true,
		);
		if (textboxMapping) return textboxMapping;
	}

	// Find standard mapping
	return (
		CC_TO_AP_MAPPINGS.find(
			(mapping) =>
				mapping.sourceType === ccType &&
				(allowMultipleValues === undefined ||
					mapping.allowMultipleValues === allowMultipleValues),
		) || null
	);
}

/**
 * Check type compatibility between source and target field types
 */
export function checkTypeCompatibility(
	sourceType: APFieldDataType | CCFieldType,
	targetType: APFieldDataType | CCFieldType,
	sourceAllowMultiple?: boolean,
	targetAllowMultiple?: boolean,
): TypeCompatibilityResult {
	// Check if there's a direct mapping
	const isApToCC = isAPFieldType(sourceType);
	const mapping = isApToCC
		? getApToCcMapping(sourceType as APFieldDataType, targetAllowMultiple)
		: getCcToApMapping(sourceType as CCFieldType, sourceAllowMultiple);

	if (mapping && mapping.targetType === targetType) {
		return {
			compatible: true,
			requiresConversion: mapping.requiresSpecialHandling || false,
			conversionNotes: mapping.notes,
		};
	}

	// Check for fallback compatibility
	if (targetType === "TEXT" || targetType === "text") {
		return {
			compatible: true,
			requiresConversion: true,
			conversionNotes: "Fallback conversion to text type",
		};
	}

	return {
		compatible: false,
		reason: `No compatible mapping found from ${sourceType} to ${targetType}`,
	};
}

/**
 * Check if field type should be skipped
 */
export function shouldSkipFieldType(
	fieldType: APFieldDataType | CCFieldType,
): boolean {
	const isAP = isAPFieldType(fieldType);
	const mappings = isAP ? AP_TO_CC_MAPPINGS : CC_TO_AP_MAPPINGS;
	const mapping = mappings.find((m) => m.sourceType === fieldType);
	return mapping?.skip === true;
}

/**
 * Helper to check if field type is AP type
 */
function isAPFieldType(type: string): type is APFieldDataType {
	return type === type.toUpperCase();
}

/**
 * Get fallback mapping for unmapped field types
 */
export function getFallbackMapping(
	sourceType: APFieldDataType | CCFieldType,
): FieldTypeMapping {
	const isAP = isAPFieldType(sourceType);
	return {
		sourceType,
		targetType: isAP ? "text" : "TEXT",
		notes: `Fallback mapping for unmapped field type '${sourceType}'`,
	};
}
