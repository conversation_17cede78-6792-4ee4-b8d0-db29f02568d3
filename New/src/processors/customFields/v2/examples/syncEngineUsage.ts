/**
 * Custom Field Sync Engine Usage Examples v2
 *
 * Comprehensive examples demonstrating how to use the v2 custom field
 * synchronization engines for field definitions, values, and standard fields.
 *
 * @fileoverview v2 Sync engine usage examples
 * @version 2.0.0
 * @since 2024-08-07
 */

import {
	CustomFieldSyncV2,
	FieldDefinitionSync,
	FieldValueSync,
	StandardFieldSync,
} from "../index.js";
import type {
	Platform,
	SyncOptions,
	ValueSyncContext,
} from "../types/index.js";

/**
 * Example 1: Complete Field Definition Synchronization
 *
 * Demonstrates how to synchronize field definitions between platforms,
 * including field matching, creation, and mapping storage.
 */
export async function exampleFieldDefinitionSync(): Promise<void> {
	console.log("=== Field Definition Sync Example ===");

	// Initialize the sync engine
	const fieldDefinitionSync = new FieldDefinitionSync({
		fieldMatchConfig: {
			strategy: "NORMALIZED" as any,
			fuzzyThreshold: 0.8,
			normalizeGermanChars: true,
		},
		typeCheckConfig: {
			allowFallbackToText: true,
			strictMultiValueMatching: false,
		},
	});

	// Configure sync options
	const syncOptions: SyncOptions = {
		requestId: "field-def-sync-001",
		dryRun: false,
		createMissingFields: true,
		includeStandardFields: false,
		logLevel: "INFO",
		maxRetries: 3,
		timeout: 30000,
	};

	try {
		// Perform field definition synchronization
		const result =
			await fieldDefinitionSync.synchronizeFieldDefinitions(syncOptions);

		console.log("Field Definition Sync Results:", {
			success: result.success,
			totalFields: result.totalFields,
			matchedFields: result.matchedFields,
			createdFields: result.createdFields,
			skippedFields: result.skippedFields,
			failedFields: result.failedFields,
			processingTimeMs: result.processingTimeMs,
		});

		// Log detailed results
		for (const fieldResult of result.results) {
			console.log(
				`Field: ${fieldResult.apField?.name || fieldResult.ccField?.name}`,
				{
					action: fieldResult.action,
					success: fieldResult.success,
					error: fieldResult.error,
					warnings: fieldResult.warnings,
				},
			);
		}

		// Log errors and warnings
		if (result.errors.length > 0) {
			console.error("Sync Errors:", result.errors);
		}
		if (result.warnings.length > 0) {
			console.warn("Sync Warnings:", result.warnings);
		}
	} catch (error) {
		console.error("Field definition sync failed:", error);
	}
}

/**
 * Example 2: Patient Field Value Synchronization
 *
 * Demonstrates how to synchronize patient custom field values between platforms
 * with proper value conversion and TEXTBOX_LIST handling.
 */
export async function exampleFieldValueSync(): Promise<void> {
	console.log("=== Field Value Sync Example ===");

	// Initialize the sync engine
	const fieldValueSync = new FieldValueSync({
		typeCheckConfig: {
			allowFallbackToText: true,
			allowTypeCoercion: true,
		},
	});

	// Configure sync context
	const syncContext: ValueSyncContext = {
		patientId: "patient-123",
		platform: "ap", // Sync from AP to CC
		fieldMappings: [], // Will be fetched automatically if empty
		standardMappings: [],
		requestId: "value-sync-001",
		dryRun: false,
	};

	try {
		// Perform patient value synchronization
		const result = await fieldValueSync.synchronizePatientValues(syncContext);

		console.log("Field Value Sync Summary:", {
			success: result.processingTimeMs > 0,
			patientId: result.patientId,
			platform: result.platform,
			totalFields: result.totalFields,
			processedFields: result.processedFields,
			successfulUpdates: result.successfulUpdates,
			failedUpdates: result.failedUpdates,
			skippedFields: result.skippedFields,
			processingTimeMs: result.processingTimeMs,
		});

		// Log field-specific results
		for (const fieldResult of result.fieldResults) {
			console.log(`Field: ${fieldResult.fieldName}`, {
				fieldType: fieldResult.fieldType,
				action: fieldResult.action,
				success: fieldResult.success,
				originalValue: fieldResult.originalValue,
				convertedValue: fieldResult.convertedValue,
				isStandardField: fieldResult.isStandardField,
				error: fieldResult.error,
				warnings: fieldResult.warnings,
			});
		}

		// Log errors and warnings
		if (result.errors.length > 0) {
			console.error("Value Sync Errors:", result.errors);
		}
		if (result.warnings.length > 0) {
			console.warn("Value Sync Warnings:", result.warnings);
		}
	} catch (error) {
		console.error("Field value sync failed:", error);
	}
}

/**
 * Example 3: Standard Field Synchronization
 *
 * Demonstrates how to synchronize standard platform fields (email, phone)
 * to custom fields on the target platform.
 */
export async function exampleStandardFieldSync(): Promise<void> {
	console.log("=== Standard Field Sync Example ===");

	// Initialize the sync engine
	const standardFieldSync = new StandardFieldSync({
		typeCheckConfig: {
			allowFallbackToText: true,
			strictMultiValueMatching: false,
		},
	});

	// Configure sync options
	const syncOptions: SyncOptions = {
		requestId: "standard-sync-001",
		dryRun: false,
		createMissingFields: true,
		includeStandardFields: true,
		logLevel: "DEBUG",
	};

	try {
		// Sync AP standard fields (email, phone) to CC custom fields
		const apToCcResult = await standardFieldSync.synchronizeStandardFields(
			"patient-123",
			"ap", // Source platform
			"cc", // Target platform
			syncOptions,
		);

		console.log("AP → CC Standard Field Sync Results:", {
			success: apToCcResult.success,
			extractedFields: apToCcResult.extractedFields,
			mappedFields: apToCcResult.mappedFields,
			syncedFields: apToCcResult.syncedFields,
			skippedFields: apToCcResult.skippedFields,
			failedFields: apToCcResult.failedFields,
			processingTimeMs: apToCcResult.processingTimeMs,
		});

		// Sync CC standard fields (patientId, profile links) to AP custom fields
		const ccToApResult = await standardFieldSync.synchronizeStandardFields(
			"patient-123",
			"cc", // Source platform
			"ap", // Target platform
			syncOptions,
		);

		console.log("CC → AP Standard Field Sync Results:", {
			success: ccToApResult.success,
			extractedFields: ccToApResult.extractedFields,
			mappedFields: ccToApResult.mappedFields,
			syncedFields: ccToApResult.syncedFields,
			skippedFields: ccToApResult.skippedFields,
			failedFields: ccToApResult.failedFields,
			processingTimeMs: ccToApResult.processingTimeMs,
		});

		// Log extraction results
		console.log("Standard Field Extractions:");
		for (const extraction of [
			...apToCcResult.extractions,
			...ccToApResult.extractions,
		]) {
			console.log(`${extraction.fieldType}:`, {
				value: extraction.value,
				isValid: extraction.isValid,
				transformedValue: extraction.transformedValue,
				error: extraction.error,
			});
		}

		// Log mapping results
		console.log("Standard Field Mappings:");
		for (const mapping of [
			...apToCcResult.mappings,
			...ccToApResult.mappings,
		]) {
			console.log(`${mapping.standardField} → ${mapping.customFieldName}:`, {
				platform: mapping.platform,
				customFieldType: mapping.customFieldType,
				required: mapping.required,
			});
		}
	} catch (error) {
		console.error("Standard field sync failed:", error);
	}
}

/**
 * Example 4: Complete Synchronization Workflow
 *
 * Demonstrates a complete synchronization workflow using the main
 * CustomFieldSyncV2 class with all sync engines.
 */
export async function exampleCompleteWorkflow(): Promise<void> {
	console.log("=== Complete Sync Workflow Example ===");

	// Initialize the main sync class
	const customFieldSync = new CustomFieldSyncV2({
		fieldMatchConfig: {
			strategy: "NORMALIZED" as any,
			fuzzyThreshold: 0.85,
			normalizeGermanChars: true,
			ignoreCase: true,
			ignoreSpaces: true,
		},
		typeCheckConfig: {
			allowFallbackToText: true,
			strictMultiValueMatching: false,
			allowTypeCoercion: true,
		},
	});

	const patientId = "patient-456";
	const requestId = "complete-workflow-001";

	try {
		// Step 1: Synchronize field definitions
		console.log("Step 1: Synchronizing field definitions...");
		const fieldDefSync = customFieldSync.getFieldDefinitionSync();
		const fieldDefResult = await fieldDefSync.synchronizeFieldDefinitions({
			requestId: `${requestId}-fields`,
			createMissingFields: true,
			logLevel: "INFO",
		});

		console.log("Field definitions synchronized:", {
			success: fieldDefResult.success,
			matchedFields: fieldDefResult.matchedFields,
			createdFields: fieldDefResult.createdFields,
		});

		// Step 2: Synchronize standard fields
		console.log("Step 2: Synchronizing standard fields...");
		const standardSync = customFieldSync.getStandardFieldSync();
		const standardResult = await standardSync.synchronizeStandardFields(
			patientId,
			"ap",
			"cc",
			{
				requestId: `${requestId}-standard`,
				includeStandardFields: true,
				logLevel: "INFO",
			},
		);

		console.log("Standard fields synchronized:", {
			success: standardResult.success,
			syncedFields: standardResult.syncedFields,
		});

		// Step 3: Synchronize patient field values
		console.log("Step 3: Synchronizing patient field values...");
		const valueSync = customFieldSync.getFieldValueSync();
		const valueResult = await valueSync.synchronizePatientValues({
			patientId,
			platform: "ap",
			fieldMappings: [],
			standardMappings: standardResult.mappings,
			requestId: `${requestId}-values`,
		});

		console.log("Patient values synchronized:", {
			success: valueResult.processingTimeMs > 0,
			successfulUpdates: valueResult.successfulUpdates,
			failedUpdates: valueResult.failedUpdates,
		});

		// Summary
		console.log("=== Complete Workflow Summary ===");
		console.log({
			fieldDefinitions: {
				matched: fieldDefResult.matchedFields,
				created: fieldDefResult.createdFields,
			},
			standardFields: {
				extracted: standardResult.extractedFields,
				synced: standardResult.syncedFields,
			},
			patientValues: {
				processed: valueResult.processedFields,
				successful: valueResult.successfulUpdates,
				failed: valueResult.failedUpdates,
			},
			totalProcessingTime:
				fieldDefResult.processingTimeMs +
				standardResult.processingTimeMs +
				valueResult.processingTimeMs,
		});
	} catch (error) {
		console.error("Complete workflow failed:", error);
	}
}

/**
 * Run all examples
 */
export async function runAllExamples(): Promise<void> {
	console.log("🚀 Running Custom Field Sync v2 Examples\n");

	await exampleFieldDefinitionSync();
	console.log("\n" + "=".repeat(50) + "\n");

	await exampleFieldValueSync();
	console.log("\n" + "=".repeat(50) + "\n");

	await exampleStandardFieldSync();
	console.log("\n" + "=".repeat(50) + "\n");

	await exampleCompleteWorkflow();
	console.log("\n✅ All examples completed!");
}

// Export individual examples for selective testing
export {
	exampleFieldDefinitionSync as fieldDefinitionExample,
	exampleFieldValueSync as fieldValueExample,
	exampleStandardFieldSync as standardFieldExample,
	exampleCompleteWorkflow as completeWorkflowExample,
};
