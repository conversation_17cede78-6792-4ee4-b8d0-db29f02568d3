/**
 * Type Checker v2
 *
 * Real field type compatibility validation based on DATA-TYPE-MAP.md rules.
 * Provides comprehensive type checking for field mappings and value conversions
 * with support for multi-value fields and TEXTBOX_LIST compatibility.
 *
 * @fileoverview v2 Real field type compatibility validation
 * @version 2.0.0
 * @since 2024-08-07
 */

import {
	BOOLEAN_RADIO_OPTIONS,
	getApToCcMapping,
	getCcToApMapping,
	shouldSkipFieldType,
	TEXTBOX_LIST_COMPATIBLE_CC_TYPES,
} from "../config/fieldMappings.js";
import type {
	APFieldDataType,
	CCFieldType,
	TypeCompatibilityResult,
} from "../types/index.js";

/**
 * Type compatibility check options
 */
export interface TypeCheckOptions {
	allowFallbackToText?: boolean; // Default: true
	strictMultiValueMatching?: boolean; // Default: false
	allowTypeCoercion?: boolean; // Default: true
}

/**
 * Detailed type compatibility information
 */
export interface DetailedTypeCompatibility extends TypeCompatibilityResult {
	sourceType: APFieldDataType | CCFieldType;
	targetType: APFieldDataType | CCFieldType;
	mappingFound: boolean;
	requiresSpecialHandling: boolean;
	conversionPath?: string[];
	multiValueSupport?: {
		sourceSupportsMulti: boolean;
		targetSupportsMulti: boolean;
		compatible: boolean;
	};
}

/**
 * Type Checker class with real validation logic
 */
export class TypeChecker {
	private options: TypeCheckOptions;

	constructor(options: Partial<TypeCheckOptions> = {}) {
		this.options = {
			allowFallbackToText: true,
			strictMultiValueMatching: false,
			allowTypeCoercion: true,
			...options,
		};
	}

	/**
	 * Check if two field types are compatible for synchronization
	 */
	public checkCompatibility(
		sourceType: APFieldDataType | CCFieldType,
		targetType: APFieldDataType | CCFieldType,
		sourceAllowMultiple?: boolean,
		targetAllowMultiple?: boolean,
	): DetailedTypeCompatibility {
		// Check if either type should be skipped
		if (shouldSkipFieldType(sourceType) || shouldSkipFieldType(targetType)) {
			return {
				compatible: false,
				sourceType,
				targetType,
				mappingFound: false,
				requiresSpecialHandling: false,
				reason: `Field type ${sourceType} or ${targetType} is marked as skipped`,
			};
		}

		// Determine conversion direction
		const isApToCC = this.isAPFieldType(sourceType);

		if (isApToCC) {
			return this.checkApToCcCompatibility(
				sourceType as APFieldDataType,
				targetType as CCFieldType,
				sourceAllowMultiple,
				targetAllowMultiple,
			);
		} else {
			return this.checkCcToApCompatibility(
				sourceType as CCFieldType,
				targetType as APFieldDataType,
				sourceAllowMultiple,
				targetAllowMultiple,
			);
		}
	}

	/**
	 * Check AP to CC field type compatibility
	 */
	private checkApToCcCompatibility(
		sourceType: APFieldDataType,
		targetType: CCFieldType,
		sourceAllowMultiple?: boolean,
		targetAllowMultiple?: boolean,
	): DetailedTypeCompatibility {
		const baseResult: Omit<DetailedTypeCompatibility, "compatible"> = {
			sourceType,
			targetType,
			mappingFound: false,
			requiresSpecialHandling: false,
		};

		// Check for direct mapping
		const mapping = getApToCcMapping(sourceType, targetAllowMultiple);
		if (mapping && mapping.targetType === targetType) {
			return {
				...baseResult,
				compatible: true,
				mappingFound: true,
				requiresSpecialHandling: mapping.requiresSpecialHandling || false,
				conversionNotes: mapping.notes,
				conversionPath: [sourceType, targetType],
			};
		}

		// Check for TEXTBOX_LIST special handling
		if (
			sourceType === "TEXTBOX_LIST" &&
			TEXTBOX_LIST_COMPATIBLE_CC_TYPES.includes(targetType)
		) {
			return {
				...baseResult,
				compatible: true,
				mappingFound: true,
				requiresSpecialHandling: true,
				conversionNotes: "TEXTBOX_LIST to multi-value field conversion",
				conversionPath: [sourceType, "multi-value", targetType],
			};
		}

		// Check for RADIO to boolean special case
		if (sourceType === "RADIO" && targetType === "boolean") {
			return {
				...baseResult,
				compatible: true,
				mappingFound: true,
				requiresSpecialHandling: true,
				conversionNotes: "RADIO Yes/No to boolean conversion",
				conversionPath: [sourceType, "boolean"],
			};
		}

		// Check for fallback to text
		if (this.options.allowFallbackToText && targetType === "text") {
			return {
				...baseResult,
				compatible: true,
				mappingFound: false,
				requiresSpecialHandling: true,
				conversionNotes: "Fallback conversion to text type",
				conversionPath: [sourceType, "text"],
			};
		}

		return {
			...baseResult,
			compatible: false,
			reason: `No compatible mapping found from AP ${sourceType} to CC ${targetType}`,
		};
	}

	/**
	 * Check CC to AP field type compatibility
	 */
	private checkCcToApCompatibility(
		sourceType: CCFieldType,
		targetType: APFieldDataType,
		sourceAllowMultiple?: boolean,
		targetAllowMultiple?: boolean,
	): DetailedTypeCompatibility {
		const baseResult: Omit<DetailedTypeCompatibility, "compatible"> = {
			sourceType,
			targetType,
			mappingFound: false,
			requiresSpecialHandling: false,
		};

		// Check for direct mapping
		const mapping = getCcToApMapping(sourceType, sourceAllowMultiple);
		if (mapping && mapping.targetType === targetType) {
			return {
				...baseResult,
				compatible: true,
				mappingFound: true,
				requiresSpecialHandling: mapping.requiresSpecialHandling || false,
				conversionNotes: mapping.notes,
				conversionPath: [sourceType, targetType],
			};
		}

		// Check for multi-value to TEXTBOX_LIST conversion
		if (
			sourceAllowMultiple &&
			targetType === "TEXTBOX_LIST" &&
			TEXTBOX_LIST_COMPATIBLE_CC_TYPES.includes(sourceType)
		) {
			return {
				...baseResult,
				compatible: true,
				mappingFound: true,
				requiresSpecialHandling: true,
				conversionNotes: "Multi-value field to TEXTBOX_LIST conversion",
				conversionPath: [sourceType, "multi-value", "TEXTBOX_LIST"],
				multiValueSupport: {
					sourceSupportsMulti: true,
					targetSupportsMulti: true,
					compatible: true,
				},
			};
		}

		// Check for boolean to RADIO conversion
		if (sourceType === "boolean" && targetType === "RADIO") {
			return {
				...baseResult,
				compatible: true,
				mappingFound: true,
				requiresSpecialHandling: true,
				conversionNotes: "Boolean to RADIO Yes/No conversion",
				conversionPath: [sourceType, "RADIO"],
			};
		}

		// Check for fallback to TEXT
		if (this.options.allowFallbackToText && targetType === "TEXT") {
			return {
				...baseResult,
				compatible: true,
				mappingFound: false,
				requiresSpecialHandling: true,
				conversionNotes: "Fallback conversion to TEXT type",
				conversionPath: [sourceType, "TEXT"],
			};
		}

		return {
			...baseResult,
			compatible: false,
			reason: `No compatible mapping found from CC ${sourceType} to AP ${targetType}`,
		};
	}

	/**
	 * Check if a field type supports multi-value
	 */
	public supportsMultiValue(
		fieldType: APFieldDataType | CCFieldType,
		platform: "ap" | "cc",
	): boolean {
		if (platform === "ap") {
			// AP fields that support multi-value
			return ["CHECKBOX", "MULTIPLE_OPTIONS", "TEXTBOX_LIST"].includes(
				fieldType as APFieldDataType,
			);
		} else {
			// CC fields support multi-value through allowMultipleValues flag
			// All CC field types can potentially support multi-value
			return true;
		}
	}

	/**
	 * Check if conversion requires special handling
	 */
	public requiresSpecialHandling(
		sourceType: APFieldDataType | CCFieldType,
		targetType: APFieldDataType | CCFieldType,
		sourceAllowMultiple?: boolean,
		targetAllowMultiple?: boolean,
	): boolean {
		const compatibility = this.checkCompatibility(
			sourceType,
			targetType,
			sourceAllowMultiple,
			targetAllowMultiple,
		);
		return compatibility.requiresSpecialHandling;
	}

	/**
	 * Get conversion path for field types
	 */
	public getConversionPath(
		sourceType: APFieldDataType | CCFieldType,
		targetType: APFieldDataType | CCFieldType,
		sourceAllowMultiple?: boolean,
		targetAllowMultiple?: boolean,
	): string[] {
		const compatibility = this.checkCompatibility(
			sourceType,
			targetType,
			sourceAllowMultiple,
			targetAllowMultiple,
		);
		return compatibility.conversionPath || [];
	}

	/**
	 * Validate field type combination
	 */
	public validateFieldTypes(
		apFieldType: APFieldDataType,
		ccFieldType: CCFieldType,
		ccAllowMultiple?: boolean,
	): { valid: boolean; reason?: string; suggestions?: string[] } {
		const compatibility = this.checkCompatibility(
			apFieldType,
			ccFieldType,
			undefined,
			ccAllowMultiple,
		);

		if (compatibility.compatible) {
			return { valid: true };
		}

		// Provide suggestions for incompatible types
		const suggestions: string[] = [];

		// Suggest fallback to text types
		if (this.options.allowFallbackToText) {
			suggestions.push("Consider using TEXT/text field types as fallback");
		}

		// Suggest TEXTBOX_LIST for multi-value scenarios
		if (
			ccAllowMultiple &&
			TEXTBOX_LIST_COMPATIBLE_CC_TYPES.includes(ccFieldType)
		) {
			suggestions.push(
				"Consider using TEXTBOX_LIST for multi-value compatibility",
			);
		}

		// Suggest boolean/RADIO conversion
		if (apFieldType === "RADIO" && ccFieldType !== "boolean") {
			suggestions.push("Consider using boolean type for RADIO fields");
		}

		return {
			valid: false,
			reason: compatibility.reason,
			suggestions: suggestions.length > 0 ? suggestions : undefined,
		};
	}

	/**
	 * Check if field type is AP type
	 */
	private isAPFieldType(type: string): boolean {
		return type === type.toUpperCase();
	}

	/**
	 * Get all compatible target types for a source type
	 */
	public getCompatibleTargetTypes(
		sourceType: APFieldDataType | CCFieldType,
		sourceAllowMultiple?: boolean,
	): Array<{
		type: APFieldDataType | CCFieldType;
		requiresSpecialHandling: boolean;
	}> {
		const compatibleTypes: Array<{
			type: APFieldDataType | CCFieldType;
			requiresSpecialHandling: boolean;
		}> = [];
		const isAP = this.isAPFieldType(sourceType);

		// Define all possible target types
		const targetTypes = isAP
			? ([
					"text",
					"textarea",
					"select",
					"boolean",
					"number",
					"telephone",
					"email",
					"date",
				] as CCFieldType[])
			: ([
					"TEXT",
					"LARGE_TEXT",
					"NUMERICAL",
					"PHONE",
					"MONETORY",
					"CHECKBOX",
					"SINGLE_OPTIONS",
					"MULTIPLE_OPTIONS",
					"DATE",
					"RADIO",
					"EMAIL",
					"TEXTBOX_LIST",
				] as APFieldDataType[]);

		for (const targetType of targetTypes) {
			const compatibility = this.checkCompatibility(
				sourceType,
				targetType,
				sourceAllowMultiple,
				undefined,
			);

			if (compatibility.compatible) {
				compatibleTypes.push({
					type: targetType,
					requiresSpecialHandling: compatibility.requiresSpecialHandling,
				});
			}
		}

		return compatibleTypes;
	}

	/**
	 * Check if value conversion is possible between types
	 */
	public canConvertValue(
		sourceType: APFieldDataType | CCFieldType,
		targetType: APFieldDataType | CCFieldType,
		sourceValue: unknown,
		sourceAllowMultiple?: boolean,
		targetAllowMultiple?: boolean,
	): { canConvert: boolean; reason?: string; warnings?: string[] } {
		// First check type compatibility
		const typeCompatibility = this.checkCompatibility(
			sourceType,
			targetType,
			sourceAllowMultiple,
			targetAllowMultiple,
		);

		if (!typeCompatibility.compatible) {
			return {
				canConvert: false,
				reason: typeCompatibility.reason,
			};
		}

		// Check value-specific constraints
		const warnings: string[] = [];

		// Check for null/undefined values
		if (sourceValue === null || sourceValue === undefined) {
			return {
				canConvert: true,
				warnings: ["Null/undefined value will be preserved"],
			};
		}

		// Check for empty string values
		if (sourceValue === "") {
			warnings.push("Empty string value will be filtered out");
		}

		// Check for TEXTBOX_LIST specific constraints
		if (sourceType === "TEXTBOX_LIST" && typeof sourceValue !== "object") {
			return {
				canConvert: false,
				reason: "TEXTBOX_LIST values must be objects",
			};
		}

		// Check for boolean conversion constraints
		if (targetType === "boolean" && typeof sourceValue === "string") {
			const normalizedValue = sourceValue.toLowerCase().trim();
			const validBooleanValues = [
				"yes",
				"ja",
				"no",
				"nein",
				"true",
				"false",
				"1",
				"0",
			];
			if (!validBooleanValues.includes(normalizedValue)) {
				return {
					canConvert: false,
					reason: `Cannot convert "${sourceValue}" to boolean`,
				};
			}
		}

		return {
			canConvert: true,
			warnings: warnings.length > 0 ? warnings : undefined,
		};
	}
}
