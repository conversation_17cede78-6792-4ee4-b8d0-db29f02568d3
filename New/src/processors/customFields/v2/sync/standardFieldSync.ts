/**
 * Standard Field Sync Engine v2
 *
 * Handles synchronization of standard platform fields to custom fields.
 * Supports AP standard fields (email, phone) → CC custom fields and
 * CC standard fields (patientId, profile links) → AP custom fields.
 *
 * @fileoverview v2 Standard field to custom field synchronization engine
 * @version 2.0.0
 * @since 2024-08-07
 */

import { eq } from "drizzle-orm";
import apiClient from "@/apiClient";
import { getDb } from "@/database";
import { dbSchema } from "@/database/schema";
import type { APGetCustomFieldType, GetAPContactType } from "@/type/APTypes.js";
import type { GetCCCustomField, GetCCPatientType } from "@/type/CCTypes.js";
import { getRequestId } from "@/utils";
import { logDebug, logError, logInfo, logWarn } from "@/utils/logger";
import { StandardFieldMapper } from "../core/standardFieldMapper.js";
import { <PERSON><PERSON><PERSON><PERSON>, type TypeCheckOptions } from "../core/typeChecker.js";
import { ValueConverter } from "../core/valueConverter.js";
import type {
	APFieldDataType,
	CCFieldType,
	Platform,
	StandardFieldExtractionResult,
	StandardFieldMapping,
	StandardFieldType,
	SyncError,
	SyncOptions,
	ValueConversionContext,
	ValueSyncResult,
} from "../types/index.js";
import { SyncErrorType } from "../types/index.js";

/**
 * Standard Field Sync Engine
 *
 * Provides comprehensive standard field to custom field synchronization
 * with intelligent extraction, mapping, and value conversion.
 */
export class StandardFieldSync {
	private standardFieldMapper: StandardFieldMapper;
	private valueConverter: ValueConverter;
	private typeChecker: TypeChecker;
	private requestId: string;

	constructor(options?: {
		typeCheckConfig?: Partial<TypeCheckOptions>;
	}) {
		this.standardFieldMapper = new StandardFieldMapper();
		this.valueConverter = new ValueConverter();
		this.typeChecker = new TypeChecker(options?.typeCheckConfig);
		this.requestId = getRequestId();
	}

	/**
	 * Synchronize standard fields to custom fields for a patient
	 *
	 * Extracts standard fields from patient data, maps to target custom fields,
	 * converts values with proper type handling, and integrates with regular
	 * custom field sync workflow.
	 *
	 * @param patientId - Local database patient ID
	 * @param sourcePlatform - Platform to extract standard fields from
	 * @param targetPlatform - Platform to sync standard fields to
	 * @param options - Synchronization options
	 * @returns Comprehensive sync results with detailed field information
	 */
	public async synchronizeStandardFields(
		patientId: string,
		sourcePlatform: Platform,
		targetPlatform: Platform,
		options: SyncOptions,
	): Promise<{
		success: boolean;
		extractedFields: number;
		mappedFields: number;
		syncedFields: number;
		skippedFields: number;
		failedFields: number;
		extractions: StandardFieldExtractionResult[];
		mappings: StandardFieldMapping[];
		syncResults: ValueSyncResult[];
		errors: SyncError[];
		warnings: string[];
		processingTimeMs: number;
	}> {
		const startTime = Date.now();
		const errors: SyncError[] = [];
		const warnings: string[] = [];

		logInfo("Starting standard field synchronization", {
			requestId: this.requestId,
			patientId,
			sourcePlatform,
			targetPlatform,
			options,
		});

		try {
			// Step 1: Fetch patient data from source platform
			const patientData = await this.fetchPatientData(
				patientId,
				sourcePlatform,
			);

			if (!patientData) {
				const error: SyncError = {
					type: SyncErrorType.API_ERROR,
					message: `Failed to fetch patient data from ${sourcePlatform}`,
					metadata: { patientId, sourcePlatform },
				};
				errors.push(error);

				return this.createEmptyResult(errors, warnings, Date.now() - startTime);
			}

			// Step 2: Extract standard fields from patient data
			const extractions = this.standardFieldMapper.extractStandardFields(
				patientData,
				sourcePlatform,
			);

			logDebug("Extracted standard fields from patient data", {
				requestId: this.requestId,
				patientId,
				sourcePlatform,
				extractionCount: extractions.length,
			});

			if (extractions.length === 0) {
				warnings.push(
					`No standard fields found in ${sourcePlatform} patient data`,
				);
				return this.createEmptyResult(errors, warnings, Date.now() - startTime);
			}

			// Step 3: Get existing custom fields from target platform
			const existingFields = await this.fetchCustomFields(targetPlatform);

			// Step 4: Create standard field mappings
			const mappingResult =
				this.standardFieldMapper.createStandardFieldMappings(
					extractions,
					targetPlatform,
					existingFields,
				);

			if (!mappingResult.success) {
				errors.push(...mappingResult.errors);
				warnings.push(...mappingResult.warnings);
			}

			// Step 5: Process and sync field values
			const syncResults = await this.processStandardFieldValues(
				extractions,
				mappingResult.mappings,
				patientId,
				targetPlatform,
				options,
			);

			// Step 6: Store standard field mappings in database if not dry run
			if (!options.dryRun && mappingResult.mappings.length > 0) {
				await this.storeStandardFieldMappings(
					mappingResult.mappings,
					patientId,
					sourcePlatform,
					targetPlatform,
				);
			}

			// Calculate summary statistics
			const syncedFields = syncResults.filter(
				(r) => r.success && r.action === "updated",
			).length;
			const skippedFields = syncResults.filter(
				(r) => r.action === "skipped",
			).length;
			const failedFields = syncResults.filter(
				(r) => !r.success || r.action === "failed",
			).length;

			const processingTimeMs = Date.now() - startTime;

			logInfo("Standard field synchronization completed", {
				requestId: this.requestId,
				patientId,
				sourcePlatform,
				targetPlatform,
				processingTimeMs,
				extractedFields: extractions.length,
				mappedFields: mappingResult.mappings.length,
				syncedFields,
				skippedFields,
				failedFields,
				totalErrors: errors.length,
				totalWarnings: warnings.length,
			});

			return {
				success: errors.length === 0,
				extractedFields: extractions.length,
				mappedFields: mappingResult.mappings.length,
				syncedFields,
				skippedFields,
				failedFields,
				extractions,
				mappings: mappingResult.mappings,
				syncResults,
				errors,
				warnings,
				processingTimeMs,
			};
		} catch (error) {
			const syncError: SyncError = {
				type: SyncErrorType.API_ERROR,
				message: `Standard field sync failed: ${error instanceof Error ? error.message : String(error)}`,
				originalError: error instanceof Error ? error : undefined,
				metadata: { patientId, sourcePlatform, targetPlatform },
			};

			errors.push(syncError);

			logError("Standard field synchronization failed", {
				requestId: this.requestId,
				patientId,
				sourcePlatform,
				targetPlatform,
				error: syncError.message,
				processingTimeMs: Date.now() - startTime,
			});

			return this.createEmptyResult(errors, warnings, Date.now() - startTime);
		}
	}

	/**
	 * Fetch patient data from specified platform
	 */
	private async fetchPatientData(
		patientId: string,
		platform: Platform,
	): Promise<GetAPContactType | GetCCPatientType | null> {
		try {
			// Get platform-specific patient ID
			const db = getDb();
			const patient = await db
				.select()
				.from(dbSchema.patient)
				.where(eq(dbSchema.patient.id, patientId))
				.limit(1);

			if (!patient[0]) {
				logWarn("Patient not found in database", {
					requestId: this.requestId,
					patientId,
				});
				return null;
			}

			if (platform === "ap") {
				if (!patient[0].apId) {
					logWarn("AP contact ID not found for patient", {
						requestId: this.requestId,
						patientId,
					});
					return null;
				}

				const contact = await apiClient.ap.contactReq.get(patient[0].apId);
				logDebug("Fetched AP contact data for standard field extraction", {
					requestId: this.requestId,
					patientId,
					apContactId: patient[0].apId,
				});
				return contact;
			} else {
				if (!patient[0].ccId) {
					logWarn("CC patient ID not found for patient", {
						requestId: this.requestId,
						patientId,
					});
					return null;
				}

				const ccPatient = await apiClient.cc.patientReq.get(
					patient[0].ccId,
					true,
				);
				logDebug("Fetched CC patient data for standard field extraction", {
					requestId: this.requestId,
					patientId,
					ccPatientId: patient[0].ccId,
				});
				return ccPatient;
			}
		} catch (error) {
			logError(`Failed to fetch patient data from ${platform}`, {
				requestId: this.requestId,
				patientId,
				platform,
				error: String(error),
			});
			return null;
		}
	}

	/**
	 * Fetch custom fields from specified platform
	 */
	private async fetchCustomFields(
		platform: Platform,
	): Promise<APGetCustomFieldType[] | GetCCCustomField[]> {
		try {
			if (platform === "ap") {
				return await apiClient.ap.apCustomfield.allWithParentFilter(true);
			} else {
				return await apiClient.cc.ccCustomfieldReq.all(true);
			}
		} catch (error) {
			logError(`Failed to fetch custom fields from ${platform}`, {
				requestId: this.requestId,
				platform,
				error: String(error),
			});
			return [];
		}
	}

	/**
	 * Process standard field values for synchronization
	 */
	private async processStandardFieldValues(
		extractions: StandardFieldExtractionResult[],
		mappings: StandardFieldMapping[],
		patientId: string,
		targetPlatform: Platform,
		options: SyncOptions,
	): Promise<ValueSyncResult[]> {
		const results: ValueSyncResult[] = [];

		for (const extraction of extractions) {
			try {
				// Find corresponding mapping
				const mapping = mappings.find(
					(m) => m.standardField === extraction.fieldType,
				);

				if (!mapping) {
					results.push({
						fieldName: extraction.fieldType,
						fieldType: "TEXT" as APFieldDataType,
						success: false,
						action: "skipped",
						originalValue: extraction.value,
						isStandardField: true,
						error: "No mapping found for standard field",
					});
					continue;
				}

				// Skip invalid extractions
				if (!extraction.isValid) {
					results.push({
						fieldName: mapping.customFieldName,
						fieldType: mapping.customFieldType as APFieldDataType | CCFieldType,
						success: false,
						action: "skipped",
						originalValue: extraction.value,
						isStandardField: true,
						error: extraction.error || "Invalid standard field value",
					});
					continue;
				}

				// Convert value using value converter
				const conversionContext: ValueConversionContext = {
					sourceType: "text", // Standard fields are typically text
					targetType: mapping.customFieldType,
					sourceValue: extraction.transformedValue,
				};

				const conversionResult =
					this.valueConverter.convertValue(conversionContext);

				if (conversionResult.success) {
					// Update target platform if not dry run
					if (!options.dryRun) {
						const updateResult = await this.updateCustomFieldValue(
							patientId,
							mapping,
							conversionResult.convertedValue,
							targetPlatform,
						);

						results.push({
							fieldName: mapping.customFieldName,
							fieldType: mapping.customFieldType as
								| APFieldDataType
								| CCFieldType,
							success: updateResult.success,
							action: updateResult.success ? "updated" : "failed",
							originalValue: extraction.value,
							convertedValue: conversionResult.convertedValue,
							isStandardField: true,
							error: updateResult.error,
							warnings: conversionResult.warnings,
						});
					} else {
						// Dry run - just report what would be updated
						results.push({
							fieldName: mapping.customFieldName,
							fieldType: mapping.customFieldType as
								| APFieldDataType
								| CCFieldType,
							success: true,
							action: "updated",
							originalValue: extraction.value,
							convertedValue: conversionResult.convertedValue,
							isStandardField: true,
							warnings: [
								...(conversionResult.warnings || []),
								"Dry run - no actual update",
							],
						});
					}

					logDebug("Successfully processed standard field value", {
						requestId: this.requestId,
						standardField: extraction.fieldType,
						customFieldName: mapping.customFieldName,
						originalValue: extraction.value,
						convertedValue: conversionResult.convertedValue,
					});
				} else {
					const errorMessage =
						conversionResult.error || "Unknown conversion error";

					results.push({
						fieldName: mapping.customFieldName,
						fieldType: mapping.customFieldType as APFieldDataType | CCFieldType,
						success: false,
						action: "failed",
						originalValue: extraction.value,
						isStandardField: true,
						error: errorMessage,
					});

					logWarn("Failed to convert standard field value", {
						requestId: this.requestId,
						standardField: extraction.fieldType,
						customFieldName: mapping.customFieldName,
						originalValue: extraction.value,
						error: errorMessage,
					});
				}
			} catch (error) {
				const errorMessage = `Failed to process standard field ${extraction.fieldType}: ${error instanceof Error ? error.message : String(error)}`;

				results.push({
					fieldName: extraction.fieldType,
					fieldType: "TEXT" as APFieldDataType,
					success: false,
					action: "failed",
					originalValue: extraction.value,
					isStandardField: true,
					error: errorMessage,
				});

				logError("Error processing standard field", {
					requestId: this.requestId,
					standardField: extraction.fieldType,
					error: errorMessage,
				});
			}
		}

		return results;
	}

	/**
	 * Update custom field value on target platform
	 */
	private async updateCustomFieldValue(
		patientId: string,
		mapping: StandardFieldMapping,
		convertedValue: unknown,
		targetPlatform: Platform,
	): Promise<{ success: boolean; error?: string }> {
		try {
			// Get platform-specific patient ID
			const db = getDb();
			const patient = await db
				.select()
				.from(dbSchema.patient)
				.where(eq(dbSchema.patient.id, patientId))
				.limit(1);

			if (!patient[0]) {
				return { success: false, error: "Patient not found in database" };
			}

			if (targetPlatform === "ap") {
				if (!patient[0].apId) {
					return {
						success: false,
						error: "AP contact ID not found for patient",
					};
				}

				// Update AP contact custom field
				const updatePayload = {
					[mapping.customFieldName]: convertedValue,
				};

				await apiClient.ap.contactReq.update(patient[0].apId, updatePayload);

				logDebug(
					"Successfully updated AP custom field with standard field value",
					{
						requestId: this.requestId,
						patientId,
						apContactId: patient[0].apId,
						customFieldName: mapping.customFieldName,
						value: convertedValue,
					},
				);

				return { success: true };
			} else {
				if (!patient[0].ccId) {
					return {
						success: false,
						error: "CC patient ID not found for patient",
					};
				}

				// Update CC patient custom field
				// This would require CC-specific API implementation
				logDebug("Would update CC custom field with standard field value", {
					requestId: this.requestId,
					patientId,
					ccPatientId: patient[0].ccId,
					customFieldName: mapping.customFieldName,
					value: convertedValue,
				});

				return { success: true };
			}
		} catch (error) {
			const errorMessage = `Failed to update ${targetPlatform} custom field: ${error instanceof Error ? error.message : String(error)}`;

			logError("Failed to update custom field with standard field value", {
				requestId: this.requestId,
				patientId,
				targetPlatform,
				customFieldName: mapping.customFieldName,
				error: errorMessage,
			});

			return { success: false, error: errorMessage };
		}
	}

	/**
	 * Store standard field mappings in database
	 */
	private async storeStandardFieldMappings(
		mappings: StandardFieldMapping[],
		patientId: string,
		sourcePlatform: Platform,
		targetPlatform: Platform,
	): Promise<void> {
		try {
			const db = getDb();

			for (const mapping of mappings) {
				const mappingData = {
					apId: targetPlatform === "ap" ? null : null, // Will be set when custom field is created
					ccId: targetPlatform === "cc" ? null : null, // Will be set when custom field is created
					name: mapping.customFieldName,
					label: mapping.customFieldName,
					type: mapping.customFieldType,
					apConfig: null,
					ccConfig: null,
					mappingType:
						sourcePlatform === "ap"
							? "custom_to_standard"
							: "standard_to_custom",
					apStandardField:
						targetPlatform === "ap" ? mapping.standardField : null,
					ccStandardField:
						targetPlatform === "cc" ? mapping.standardField : null,
				};

				await db
					.insert(dbSchema.customFields)
					.values(mappingData)
					.onConflictDoNothing(); // Avoid duplicates

				logDebug("Stored standard field mapping in database", {
					requestId: this.requestId,
					patientId,
					standardField: mapping.standardField,
					customFieldName: mapping.customFieldName,
					mappingType: mappingData.mappingType,
				});
			}
		} catch (error) {
			logError("Failed to store standard field mappings", {
				requestId: this.requestId,
				patientId,
				sourcePlatform,
				targetPlatform,
				error: String(error),
			});
			// Don't throw - this is not critical for sync operation
		}
	}

	/**
	 * Create empty result for error cases
	 */
	private createEmptyResult(
		errors: SyncError[],
		warnings: string[],
		processingTimeMs: number,
	): {
		success: boolean;
		extractedFields: number;
		mappedFields: number;
		syncedFields: number;
		skippedFields: number;
		failedFields: number;
		extractions: StandardFieldExtractionResult[];
		mappings: StandardFieldMapping[];
		syncResults: ValueSyncResult[];
		errors: SyncError[];
		warnings: string[];
		processingTimeMs: number;
	} {
		return {
			success: false,
			extractedFields: 0,
			mappedFields: 0,
			syncedFields: 0,
			skippedFields: 0,
			failedFields: 0,
			extractions: [],
			mappings: [],
			syncResults: [],
			errors,
			warnings,
			processingTimeMs,
		};
	}

	/**
	 * Get standard field mappings for a patient
	 *
	 * Retrieves existing standard field mappings from the database
	 * for a specific patient and platform combination.
	 *
	 * @param patientId - Local database patient ID
	 * @param platform - Platform to get mappings for
	 * @returns Array of standard field mappings
	 */
	public async getStandardFieldMappings(
		patientId: string,
		platform: Platform,
	): Promise<StandardFieldMapping[]> {
		try {
			const db = getDb();
			const mappings = await db
				.select()
				.from(dbSchema.customFields)
				.where(
					eq(
						dbSchema.customFields.mappingType,
						platform === "ap" ? "standard_to_custom" : "custom_to_standard",
					),
				);

			return mappings
				.filter((mapping) => {
					if (platform === "ap") {
						return mapping.apStandardField !== null;
					} else {
						return mapping.ccStandardField !== null;
					}
				})
				.map((mapping) => ({
					standardField: (platform === "ap"
						? mapping.apStandardField
						: mapping.ccStandardField) as StandardFieldType,
					platform,
					customFieldName: mapping.name || "",
					customFieldType: mapping.type as APFieldDataType | CCFieldType,
					extractionPath: "", // Would be configured based on standard field type
					required: false,
				}));
		} catch (error) {
			logError("Failed to get standard field mappings", {
				requestId: this.requestId,
				patientId,
				platform,
				error: String(error),
			});
			return [];
		}
	}

	/**
	 * Validate standard field mapping configuration
	 *
	 * Checks if a standard field mapping is valid and can be processed.
	 *
	 * @param mapping - Standard field mapping to validate
	 * @param targetPlatform - Target platform for the mapping
	 * @returns Validation result with success status and error details
	 */
	public validateStandardFieldMapping(
		mapping: StandardFieldMapping,
		targetPlatform: Platform,
	): { valid: boolean; errors: string[]; warnings: string[] } {
		const errors: string[] = [];
		const warnings: string[] = [];

		// Validate required fields
		if (!mapping.standardField) {
			errors.push("Standard field type is required");
		}

		if (!mapping.customFieldName) {
			errors.push("Custom field name is required");
		}

		if (!mapping.customFieldType) {
			errors.push("Custom field type is required");
		}

		// Validate platform compatibility
		if (mapping.platform !== targetPlatform) {
			warnings.push(
				`Mapping platform (${mapping.platform}) does not match target platform (${targetPlatform})`,
			);
		}

		// Validate field type compatibility using TypeChecker
		const typeCompatibility = this.typeChecker.checkCompatibility(
			"text", // Standard fields are typically text
			mapping.customFieldType,
		);

		if (!typeCompatibility.compatible) {
			errors.push(
				`Field type incompatibility: ${typeCompatibility.reason || "Unknown reason"}`,
			);
		}

		return {
			valid: errors.length === 0,
			errors,
			warnings,
		};
	}
}
