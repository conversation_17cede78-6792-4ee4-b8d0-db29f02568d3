/**
 * Field Value Sync Engine v2
 *
 * Handles synchronization of custom field values between AutoPatient (AP)
 * and CliniCore (CC) platforms. Provides patient data fetching, value conversion,
 * and comprehensive sync results with TEXTBOX_LIST special handling.
 *
 * @fileoverview v2 Field value synchronization engine
 * @version 2.0.0
 * @since 2024-08-07
 */

import { eq } from "drizzle-orm";
import apiClient from "@/apiClient";
import { getDb } from "@/database";
import { dbSchema } from "@/database/schema";
import type { APGetCustomFieldType, GetAPContactType } from "@/type/APTypes.js";
import type {
	GetCCCustomField,
	GetCCPatientCustomField,
	GetCCPatientType,
} from "@/type/CCTypes.js";
import { getRequestId } from "@/utils";
import { logDebug, logError, logInfo, logWarn } from "@/utils/logger";
import { TypeChe<PERSON>, type TypeCheckOptions } from "../core/typeChecker.js";
import { ValueConverter } from "../core/valueConverter.js";
import type {
	APFieldDataType,
	CCFieldType,
	FieldMapping,
	Platform,
	SyncError,
	SyncSummary,
	ValueConversionContext,
	ValueSyncContext,
	ValueSyncResult,
} from "../types/index.js";
import { FieldMatchStrategy, SyncErrorType } from "../types/index.js";

/**
 * Patient data structure for value sync
 */
interface PatientData {
	id: string | number;
	customFields: Array<{
		id: string | number;
		value?: unknown;
		field_value?: Record<string, string>;
		field?: GetCCCustomField;
		values?: Array<{ id?: number; value?: string }>;
	}>;
}

/**
 * Field Value Sync Engine
 *
 * Provides comprehensive patient custom field value synchronization
 * with intelligent value conversion and error handling.
 */
export class FieldValueSync {
	private valueConverter: ValueConverter;
	private typeChecker: TypeChecker;
	private requestId: string;

	constructor(options?: {
		typeCheckConfig?: Partial<TypeCheckOptions>;
	}) {
		this.valueConverter = new ValueConverter();
		this.typeChecker = new TypeChecker(options?.typeCheckConfig);
		this.requestId = getRequestId();
	}

	/**
	 * Synchronize patient custom field values
	 *
	 * Fetches patient data from source platform, gets field mappings,
	 * converts values using unified converter, and updates target platform.
	 *
	 * @param context - Value synchronization context
	 * @returns Comprehensive sync summary with detailed results
	 */
	public async synchronizePatientValues(
		context: ValueSyncContext,
	): Promise<SyncSummary> {
		const startTime = new Date();
		const fieldResults: ValueSyncResult[] = [];
		const errors: string[] = [];
		const warnings: string[] = [];

		logInfo("Starting patient custom field value synchronization", {
			requestId: this.requestId,
			patientId: context.patientId,
			platform: context.platform,
			dryRun: context.dryRun,
		});

		try {
			// Step 1: Fetch patient data from source platform
			const patientData = await this.fetchPatientData(
				context.patientId,
				context.platform,
			);

			if (!patientData) {
				errors.push(`Failed to fetch patient data from ${context.platform}`);
				return this.createSyncSummary(
					context,
					startTime,
					fieldResults,
					errors,
					warnings,
				);
			}

			// Step 2: Get field mappings for conversion
			const fieldMappings =
				context.fieldMappings.length > 0
					? context.fieldMappings
					: await this.getFieldMappings();

			if (fieldMappings.length === 0) {
				warnings.push("No field mappings found, skipping value sync");
				return this.createSyncSummary(
					context,
					startTime,
					fieldResults,
					errors,
					warnings,
				);
			}

			logDebug("Retrieved field mappings for value conversion", {
				requestId: this.requestId,
				mappingCount: fieldMappings.length,
			});

			// Step 3: Process custom field values
			const customFieldResults = await this.processCustomFieldValues(
				patientData,
				fieldMappings,
				context,
			);

			fieldResults.push(...customFieldResults.results);
			errors.push(...customFieldResults.errors);
			warnings.push(...customFieldResults.warnings);

			// Step 4: Process standard field values if enabled
			if (context.standardMappings.length > 0) {
				const standardFieldResults = await this.processStandardFieldValues(
					patientData,
					context.standardMappings,
					context,
				);

				fieldResults.push(...standardFieldResults.results);
				errors.push(...standardFieldResults.errors);
				warnings.push(...standardFieldResults.warnings);
			}

			// Step 5: Update target platform with converted values
			if (!context.dryRun && fieldResults.some((r) => r.success)) {
				const updateResults = await this.updateTargetPlatform(
					fieldResults.filter((r) => r.success),
					context,
				);

				// Update field results with actual update status
				for (const result of fieldResults) {
					const updateResult = updateResults.find(
						(ur) => ur.fieldName === result.fieldName,
					);
					if (updateResult) {
						result.action = updateResult.success ? "updated" : "failed";
						if (!updateResult.success && updateResult.error) {
							result.error = updateResult.error;
							result.success = false;
						}
					}
				}
			}

			return this.createSyncSummary(
				context,
				startTime,
				fieldResults,
				errors,
				warnings,
			);
		} catch (error) {
			const errorMessage = `Patient value sync failed: ${error instanceof Error ? error.message : String(error)}`;
			errors.push(errorMessage);

			logError("Patient custom field value synchronization failed", {
				requestId: this.requestId,
				patientId: context.patientId,
				platform: context.platform,
				error: errorMessage,
			});

			return this.createSyncSummary(
				context,
				startTime,
				fieldResults,
				errors,
				warnings,
			);
		}
	}

	/**
	 * Fetch patient data from specified platform
	 */
	private async fetchPatientData(
		patientId: string,
		platform: Platform,
	): Promise<PatientData | null> {
		try {
			if (platform === "ap") {
				return await this.fetchApPatientData(patientId);
			} else {
				return await this.fetchCcPatientData(patientId);
			}
		} catch (error) {
			logError(`Failed to fetch patient data from ${platform}`, {
				requestId: this.requestId,
				patientId,
				platform,
				error: String(error),
			});
			return null;
		}
	}

	/**
	 * Fetch patient data from AutoPatient platform
	 */
	private async fetchApPatientData(
		patientId: string,
	): Promise<PatientData | null> {
		try {
			// Get patient lookup to find AP contact ID
			const db = getDb();
			const patient = await db
				.select()
				.from(dbSchema.patient)
				.where(eq(dbSchema.patient.id, patientId))
				.limit(1);

			if (!patient[0]?.apId) {
				logWarn("AP contact ID not found for patient", {
					requestId: this.requestId,
					patientId,
				});
				return null;
			}

			// Fetch contact data with custom fields
			const contact: GetAPContactType = await apiClient.ap.contactReq.get(
				patient[0].apId,
			);

			logInfo("Fetched AP contact data", {
				requestId: this.requestId,
				patientId,
				apContactId: patient[0].apId,
				customFieldCount: contact.customFields?.length || 0,
			});

			// Convert to standardized format
			const customFields =
				contact.customFields?.map((cf) => ({
					id: cf.id,
					value: cf.value,
					field_value: cf.field_value,
				})) || [];

			return {
				id: patient[0].apId,
				customFields,
			};
		} catch (error) {
			logError("Failed to fetch AP patient data", {
				requestId: this.requestId,
				patientId,
				error: String(error),
			});
			return null;
		}
	}

	/**
	 * Fetch patient data from CliniCore platform
	 */
	private async fetchCcPatientData(
		patientId: string,
	): Promise<PatientData | null> {
		try {
			// Get patient lookup to find CC patient ID
			const db = getDb();
			const patient = await db
				.select()
				.from(dbSchema.patient)
				.where(eq(dbSchema.patient.id, patientId))
				.limit(1);

			if (!patient[0]?.ccId) {
				logWarn("CC patient ID not found for patient", {
					requestId: this.requestId,
					patientId,
				});
				return null;
			}

			// Fetch patient data with cache invalidation
			const ccPatient: GetCCPatientType = await apiClient.cc.patientReq.get(
				patient[0].ccId,
				true,
			);

			// Fetch custom field values if patient has custom fields
			let customFields: Array<{
				id: number;
				field?: GetCCCustomField;
				values?: Array<{ id?: number; value?: string }>;
			}> = [];

			if (ccPatient.customFields && ccPatient.customFields.length > 0) {
				const customFieldData: GetCCPatientCustomField[] =
					await apiClient.cc.patientReq.customFields(
						ccPatient.customFields,
						true,
					);

				customFields = customFieldData.map((cf) => ({
					id: cf.field.id,
					field: cf.field,
					values: cf.values || [],
				}));
			}

			logInfo("Fetched CC patient data", {
				requestId: this.requestId,
				patientId,
				ccPatientId: patient[0].ccId,
				customFieldCount: customFields.length,
			});

			return {
				id: patient[0].ccId,
				customFields,
			};
		} catch (error) {
			logError("Failed to fetch CC patient data", {
				requestId: this.requestId,
				patientId,
				error: String(error),
			});
			return null;
		}
	}

	/**
	 * Get field mappings from database
	 */
	private async getFieldMappings(): Promise<FieldMapping[]> {
		try {
			const db = getDb();
			const mappings = await db
				.select()
				.from(dbSchema.customFields)
				.where(eq(dbSchema.customFields.mappingType, "custom_to_custom"));

			return mappings.map((mapping) => ({
				id: mapping.id,
				apFieldId: mapping.apId || undefined,
				ccFieldId: mapping.ccId || undefined,
				apFieldName: mapping.name || "",
				ccFieldName: mapping.label || mapping.name || "",
				apFieldType: (mapping.apConfig?.dataType || "TEXT") as APFieldDataType,
				ccFieldType: (mapping.ccConfig?.type || "text") as CCFieldType,
				matchStrategy: FieldMatchStrategy.EXACT,
				confidence: 1.0,
				isStandardField: false,
				isActive: true,
				createdAt: mapping.createdAt,
				updatedAt: mapping.updatedAt,
			}));
		} catch (error) {
			logError("Failed to get field mappings", {
				requestId: this.requestId,
				error: String(error),
			});
			return [];
		}
	}

	/**
	 * Process custom field values for conversion
	 */
	private async processCustomFieldValues(
		patientData: PatientData,
		fieldMappings: FieldMapping[],
		context: ValueSyncContext,
	): Promise<{
		results: ValueSyncResult[];
		errors: string[];
		warnings: string[];
	}> {
		const results: ValueSyncResult[] = [];
		const errors: string[] = [];
		const warnings: string[] = [];

		// Create lookup maps for efficient field mapping
		const apFieldMap = new Map(
			fieldMappings.filter((m) => m.apFieldId).map((m) => [m.apFieldId!, m]),
		);
		const ccFieldMap = new Map(
			fieldMappings.filter((m) => m.ccFieldId).map((m) => [m.ccFieldId!, m]),
		);

		for (const customField of patientData.customFields) {
			try {
				let mapping: FieldMapping | undefined;
				let sourceValue: unknown;
				let targetFieldType: string;

				// Determine mapping and extract value based on platform
				if (context.platform === "ap") {
					mapping = apFieldMap.get(String(customField.id));
					sourceValue = customField.field_value || customField.value;
					targetFieldType = mapping?.ccFieldType || "text";
				} else {
					mapping = ccFieldMap.get(Number(customField.id));
					sourceValue = customField.values?.map((v) => v.value).filter(Boolean);
					targetFieldType = mapping?.apFieldType || "TEXT";
				}

				if (!mapping) {
					warnings.push(
						`No mapping found for ${context.platform} field ID: ${customField.id}`,
					);
					continue;
				}

				// Skip empty values unless they need to be cleared
				if (this.isEmptyValue(sourceValue)) {
					results.push({
						fieldName:
							context.platform === "ap"
								? mapping.ccFieldName
								: mapping.apFieldName,
						fieldType: targetFieldType as APFieldDataType | CCFieldType,
						success: true,
						action: "skipped",
						originalValue: sourceValue,
						isStandardField: false,
						warnings: ["Empty value skipped"],
					});
					continue;
				}

				// Convert value using value converter
				const conversionContext: ValueConversionContext = {
					sourceType:
						context.platform === "ap"
							? mapping.apFieldType
							: mapping.ccFieldType,
					targetType: targetFieldType as APFieldDataType | CCFieldType,
					sourceValue,
					isMultiValue: context.platform === "cc" && Array.isArray(sourceValue),
				};

				const conversionResult =
					this.valueConverter.convertValue(conversionContext);

				if (conversionResult.success) {
					results.push({
						fieldName:
							context.platform === "ap"
								? mapping.ccFieldName
								: mapping.apFieldName,
						fieldType: targetFieldType as APFieldDataType | CCFieldType,
						success: true,
						action: "updated",
						originalValue: sourceValue,
						convertedValue: conversionResult.convertedValue,
						isStandardField: false,
						warnings: conversionResult.warnings,
					});

					logDebug("Successfully converted field value", {
						requestId: this.requestId,
						fieldId: customField.id,
						fieldName: mapping.apFieldName,
						sourceType: conversionContext.sourceType,
						targetType: conversionContext.targetType,
						originalValue: sourceValue,
						convertedValue: conversionResult.convertedValue,
					});
				} else {
					const errorMessage =
						conversionResult.error || "Unknown conversion error";
					errors.push(
						`Failed to convert value for field "${mapping.apFieldName}": ${errorMessage}`,
					);

					results.push({
						fieldName:
							context.platform === "ap"
								? mapping.ccFieldName
								: mapping.apFieldName,
						fieldType: targetFieldType as APFieldDataType | CCFieldType,
						success: false,
						action: "failed",
						originalValue: sourceValue,
						isStandardField: false,
						error: errorMessage,
					});
				}
			} catch (error) {
				const errorMessage = `Failed to process custom field ${customField.id}: ${error instanceof Error ? error.message : String(error)}`;
				errors.push(errorMessage);

				results.push({
					fieldName: `field_${customField.id}`,
					fieldType: "TEXT" as APFieldDataType,
					success: false,
					action: "failed",
					originalValue: customField.value || customField.values,
					isStandardField: false,
					error: errorMessage,
				});
			}
		}

		return { results, errors, warnings };
	}

	/**
	 * Process standard field values for conversion
	 */
	private async processStandardFieldValues(
		patientData: PatientData,
		standardMappings: any[],
		context: ValueSyncContext,
	): Promise<{
		results: ValueSyncResult[];
		errors: string[];
		warnings: string[];
	}> {
		const results: ValueSyncResult[] = [];
		const errors: string[] = [];
		const warnings: string[] = [];

		// Standard field processing would be implemented here
		// For now, return empty results as this is handled by StandardFieldSync
		warnings.push("Standard field processing delegated to StandardFieldSync");

		return { results, errors, warnings };
	}

	/**
	 * Update target platform with converted values
	 */
	private async updateTargetPlatform(
		successfulResults: ValueSyncResult[],
		context: ValueSyncContext,
	): Promise<Array<{ fieldName: string; success: boolean; error?: string }>> {
		const updateResults: Array<{
			fieldName: string;
			success: boolean;
			error?: string;
		}> = [];

		try {
			if (context.platform === "ap") {
				// Update CC with AP values
				const updateResult = await this.updateCcPatientCustomFields(
					context.patientId,
					successfulResults,
				);
				updateResults.push(...updateResult);
			} else {
				// Update AP with CC values
				const updateResult = await this.updateApPatientCustomFields(
					context.patientId,
					successfulResults,
				);
				updateResults.push(...updateResult);
			}
		} catch (error) {
			logError("Failed to update target platform", {
				requestId: this.requestId,
				patientId: context.patientId,
				platform: context.platform,
				error: String(error),
			});

			// Mark all results as failed
			for (const result of successfulResults) {
				updateResults.push({
					fieldName: result.fieldName,
					success: false,
					error: `Platform update failed: ${error instanceof Error ? error.message : String(error)}`,
				});
			}
		}

		return updateResults;
	}

	/**
	 * Update AP patient custom fields
	 */
	private async updateApPatientCustomFields(
		patientId: string,
		results: ValueSyncResult[],
	): Promise<Array<{ fieldName: string; success: boolean; error?: string }>> {
		const updateResults: Array<{
			fieldName: string;
			success: boolean;
			error?: string;
		}> = [];

		try {
			// Get AP contact ID
			const db = getDb();
			const patient = await db
				.select()
				.from(dbSchema.patient)
				.where(eq(dbSchema.patient.id, patientId))
				.limit(1);

			if (!patient[0]?.apId) {
				throw new Error("AP contact ID not found for patient");
			}

			// Prepare update payload for AP
			const updatePayload: Record<string, unknown> = {};

			for (const result of results) {
				if (result.success && result.convertedValue !== undefined) {
					// Handle TEXTBOX_LIST special case with Record<string, string> format
					if (
						result.fieldType === "TEXTBOX_LIST" &&
						typeof result.convertedValue === "object"
					) {
						updatePayload[result.fieldName] = result.convertedValue;
					} else {
						updatePayload[result.fieldName] = result.convertedValue;
					}
				}
			}

			if (Object.keys(updatePayload).length > 0) {
				await apiClient.ap.contactReq.update(patient[0].apId, updatePayload);

				// Mark all fields as successfully updated
				for (const result of results) {
					updateResults.push({
						fieldName: result.fieldName,
						success: true,
					});
				}

				logInfo("Successfully updated AP patient custom fields", {
					requestId: this.requestId,
					patientId,
					apContactId: patient[0].apId,
					updatedFieldCount: Object.keys(updatePayload).length,
				});
			}
		} catch (error) {
			logError("Failed to update AP patient custom fields", {
				requestId: this.requestId,
				patientId,
				error: String(error),
			});

			// Mark all fields as failed
			for (const result of results) {
				updateResults.push({
					fieldName: result.fieldName,
					success: false,
					error: `AP update failed: ${error instanceof Error ? error.message : String(error)}`,
				});
			}
		}

		return updateResults;
	}

	/**
	 * Update CC patient custom fields
	 */
	private async updateCcPatientCustomFields(
		patientId: string,
		results: ValueSyncResult[],
	): Promise<Array<{ fieldName: string; success: boolean; error?: string }>> {
		const updateResults: Array<{
			fieldName: string;
			success: boolean;
			error?: string;
		}> = [];

		try {
			// Get CC patient ID
			const db = getDb();
			const patient = await db
				.select()
				.from(dbSchema.patient)
				.where(eq(dbSchema.patient.id, patientId))
				.limit(1);

			if (!patient[0]?.ccId) {
				throw new Error("CC patient ID not found for patient");
			}

			// Update each field individually for CC
			for (const result of results) {
				try {
					if (result.success && result.convertedValue !== undefined) {
						// CC expects array of values
						const values = Array.isArray(result.convertedValue)
							? result.convertedValue
							: [result.convertedValue];

						// Update field via CC API (implementation would depend on CC API structure)
						// This is a placeholder - actual implementation would use CC's custom field update API
						logDebug("Would update CC custom field", {
							requestId: this.requestId,
							fieldName: result.fieldName,
							values,
						});

						updateResults.push({
							fieldName: result.fieldName,
							success: true,
						});
					}
				} catch (error) {
					updateResults.push({
						fieldName: result.fieldName,
						success: false,
						error: `CC field update failed: ${error instanceof Error ? error.message : String(error)}`,
					});
				}
			}

			logInfo("Successfully updated CC patient custom fields", {
				requestId: this.requestId,
				patientId,
				ccPatientId: patient[0].ccId,
				updatedFieldCount: updateResults.filter((r) => r.success).length,
			});
		} catch (error) {
			logError("Failed to update CC patient custom fields", {
				requestId: this.requestId,
				patientId,
				error: String(error),
			});

			// Mark all fields as failed
			for (const result of results) {
				updateResults.push({
					fieldName: result.fieldName,
					success: false,
					error: `CC update failed: ${error instanceof Error ? error.message : String(error)}`,
				});
			}
		}

		return updateResults;
	}

	/**
	 * Check if a value is considered empty
	 */
	private isEmptyValue(value: unknown): boolean {
		if (value === null || value === undefined) {
			return true;
		}

		if (typeof value === "string" && value.trim() === "") {
			return true;
		}

		if (Array.isArray(value) && value.length === 0) {
			return true;
		}

		if (
			Array.isArray(value) &&
			value.every((v) => v === "" || v === null || v === undefined)
		) {
			return true;
		}

		if (typeof value === "object" && value !== null) {
			const obj = value as Record<string, unknown>;
			return (
				Object.keys(obj).length === 0 ||
				Object.values(obj).every(
					(v) => v === "" || v === null || v === undefined,
				)
			);
		}

		return false;
	}

	/**
	 * Create sync summary from results
	 */
	private createSyncSummary(
		context: ValueSyncContext,
		startTime: Date,
		fieldResults: ValueSyncResult[],
		errors: string[],
		warnings: string[],
	): SyncSummary {
		const endTime = new Date();
		const processingTimeMs = endTime.getTime() - startTime.getTime();

		const successfulUpdates = fieldResults.filter(
			(r) => r.success && r.action === "updated",
		).length;
		const failedUpdates = fieldResults.filter(
			(r) => !r.success || r.action === "failed",
		).length;
		const skippedFields = fieldResults.filter(
			(r) => r.action === "skipped",
		).length;
		const standardFieldsProcessed = fieldResults.filter(
			(r) => r.isStandardField,
		).length;
		const customFieldsProcessed = fieldResults.filter(
			(r) => !r.isStandardField,
		).length;

		return {
			patientId: context.patientId,
			platform: context.platform,
			requestId: context.requestId,
			startTime,
			endTime,
			processingTimeMs,
			totalFields: fieldResults.length,
			processedFields: fieldResults.filter((r) => r.action !== "skipped")
				.length,
			successfulUpdates,
			failedUpdates,
			skippedFields,
			standardFieldsProcessed,
			customFieldsProcessed,
			fieldResults,
			errors,
			warnings,
		};
	}
}
