/**
 * Test Script for Phase 3 Orchestration Methods
 * 
 * Simple test to verify the new synchronizeFields and synchronizePatientValues
 * methods work correctly in the CustomFieldSyncV2 class.
 * 
 * @fileoverview Test script for v2 orchestration methods
 * @version 2.0.0
 * @since 2024-08-07
 */

import { CustomFieldSyncV2 } from "./index.js";
import type { APGetCustomFieldType } from "@/type/APTypes.js";
import type { GetCCCustomField } from "@/type/CCTypes.js";

/**
 * Test the synchronizeFields method with mock data
 */
async function testSynchronizeFields(): Promise<void> {
	console.log("🧪 Testing synchronizeFields method...");

	const customFieldSync = new CustomFieldSyncV2({
		fieldMatchConfig: {
			strategy: "NORMALIZED" as any,
			fuzzyThreshold: 0.8,
		},
		typeCheckConfig: {
			allowFallbackToText: true,
		},
	});

	// Mock AP fields
	const mockApFields: APGetCustomFieldType[] = [
		{
			id: "test-ap-1",
			name: "Test Email Field",
			dataType: "TEXT",
			isRequired: false,
			isActive: true,
			textBoxListOptions: [],
			options: [],
		} as APGetCustomFieldType,
	];

	// Mock CC fields
	const mockCcFields: GetCCCustomField[] = [
		{
			id: 1,
			name: "test_email",
			label: "Test Email",
			validation: "{}",
			type: "email",
			color: null,
			positions: [],
			allowMultipleValues: false,
			useCustomSort: null,
			isRequired: false,
			allowedValues: [],
			defaultValues: [],
		},
	];

	try {
		const result = await customFieldSync.synchronizeFields(
			mockApFields,
			mockCcFields,
			{
				requestId: "test-sync-fields-001",
				dryRun: true,
				createMissingFields: false,
				logLevel: "INFO",
			}
		);

		console.log("✅ synchronizeFields test completed:", {
			success: result.success,
			totalFields: result.totalFields,
			matchedFields: result.matchedFields,
			processingTimeMs: result.processingTimeMs,
		});

		return;
	} catch (error) {
		console.error("❌ synchronizeFields test failed:", error);
		throw error;
	}
}

/**
 * Test the synchronizePatientValues method with mock data
 */
async function testSynchronizePatientValues(): Promise<void> {
	console.log("🧪 Testing synchronizePatientValues method...");

	const customFieldSync = new CustomFieldSyncV2({
		typeCheckConfig: {
			allowFallbackToText: true,
		},
	});

	// Mock patient data
	const mockPatientData = {
		id: "test-patient-123",
		customFields: [
			{
				id: "test-field-1",
				value: "<EMAIL>",
			},
		],
	};

	// Mock field mappings
	const mockFieldMappings = [
		{
			id: "test-mapping-1",
			apFieldId: "test-field-1",
			ccFieldId: 1,
			apFieldName: "Test Email",
			ccFieldName: "test_email",
			apFieldType: "TEXT" as any,
			ccFieldType: "email" as any,
			matchStrategy: "EXACT" as any,
			confidence: 1.0,
			isStandardField: false,
			isActive: true,
		},
	];

	try {
		const result = await customFieldSync.synchronizePatientValues(
			mockPatientData,
			mockFieldMappings,
			{
				requestId: "test-sync-values-001",
				dryRun: true,
				logLevel: "INFO",
			}
		);

		console.log("✅ synchronizePatientValues test completed:", {
			success: result.processingTimeMs > 0,
			patientId: result.patientId,
			platform: result.platform,
			processedFields: result.processedFields,
			processingTimeMs: result.processingTimeMs,
		});

		return;
	} catch (error) {
		console.error("❌ synchronizePatientValues test failed:", error);
		throw error;
	}
}

/**
 * Run all orchestration tests
 */
export async function runOrchestrationTests(): Promise<void> {
	console.log("🚀 Running Phase 3 Orchestration Tests\n");

	try {
		await testSynchronizeFields();
		console.log("");
		
		await testSynchronizePatientValues();
		console.log("");

		console.log("✅ All orchestration tests completed successfully!");
	} catch (error) {
		console.error("❌ Orchestration tests failed:", error);
		process.exit(1);
	}
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
	runOrchestrationTests().catch(console.error);
}
